'use client'

import { createTheme, MantineColorsTuple, MantineThemeOverride } from '@mantine/core'

const greenColors: MantineColorsTuple = [
  'oklch(0.982 0.018 155.826)',
  'oklch(0.962 0.044 156.743)',
  'oklch(0.925 0.084 155.995)',
  'oklch(0.871 0.15 154.449)',
  'oklch(0.792 0.209 151.711)',
  'oklch(0.723 0.219 149.579)',
  'oklch(0.627 0.194 149.214)',
  'oklch(0.527 0.154 150.069)',
  'oklch(0.448 0.119 151.328)',
  'oklch(0.393 0.095 152.535)',
  'oklch(0.266 0.065 152.934)',
]

const yellowColors: MantineColorsTuple = [
  'oklch(0.987 0.026 102.212)',
  'oklch(0.973 0.071 103.193)',
  'oklch(0.945 0.129 101.54)',
  'oklch(0.905 0.182 98.111)',
  'oklch(0.852 0.199 91.936)',
  'oklch(0.795 0.184 86.047)',
  'oklch(0.681 0.162 75.834)',
  'oklch(0.554 0.135 66.442)',
  'oklch(0.476 0.114 61.907)',
  'oklch(0.421 0.095 57.708)',
  'oklch(0.286 0.066 53.813)',
]

const amberColors: MantineColorsTuple = [
  'oklch(0.987 0.022 95.277)',
  'oklch(0.962 0.059 95.617)',
  'oklch(0.924 0.12 95.746)',
  'oklch(0.879 0.169 91.605)',
  'oklch(0.828 0.189 84.429)',
  'oklch(0.769 0.188 70.08)',
  'oklch(0.666 0.179 58.318)',
  'oklch(0.555 0.163 48.998)',
  'oklch(0.473 0.137 46.201)',
  'oklch(0.414 0.112 45.904)',
  'oklch(0.279 0.077 45.635)',
]

const redColors: MantineColorsTuple = [
  'oklch(0.971 0.013 17.38)',
  'oklch(0.936 0.032 17.717)',
  'oklch(0.885 0.062 18.334)',
  'oklch(0.808 0.114 19.571)',
  'oklch(0.704 0.191 22.216)',
  'oklch(0.637 0.237 25.331)',
  'oklch(0.577 0.245 27.325)',
  'oklch(0.505 0.213 27.518)',
  'oklch(0.444 0.177 26.899)',
  'oklch(0.396 0.141 25.723)',
  'oklch(0.258 0.092 26.042)',
]

const theme: MantineThemeOverride = createTheme({
  // Colors
  colors: {
    primary: greenColors,
    secondary: yellowColors,

    info: blueColors,
    success: greenColors,
    warning: amberColors,
    error: redColors,
  },
  primaryColor: 'primary',
  defaultGradient: {
    from: 'primary',
    to: 'secondary',
    deg: 90,
  },
  defaultRadius: 'md',

  // Typography
  fontFamily: 'var(--font-sans), sans-serif',

  // Components
  components: {
    ActionIcon: {
      defaultProps: {
        size: 24,
      },
    },
  },
})

export default theme
