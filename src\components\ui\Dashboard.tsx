'use client'

import React from 'react'
import Link from 'next/link'
import {
  ActionIcon,
  AppShell,
  Avatar,
  Box,
  Group,
  Image,
  Menu,
  NavLink,
  ScrollArea,
  Text,
  Title,
  UnstyledButton,
} from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import {
  AltArrowRight,
  Bell,
  CloseCircle,
  Documents,
  HamburgerMenu,
  Logout2,
  Settings,
  User,
  Widget,
} from '@solar-icons/react'

const user = {
  name: 'System',
  avatar: '/system.svg',
}

const navigationData = [
  {
    label: 'Dashboard',
    icon: Widget,
    href: '/',
  },
  {
    label: 'Market News',
    icon: Documents,
    children: [
      { label: 'Overview', href: '/market-news/overview' },
      { label: 'Forecasts', href: '/market-news/forecasts' },
      { label: 'Outlook', href: '/market-news/outlook' },
      { label: 'Real time', href: '/market-news/real-time' },
    ],
  },
]

export function Dashboard() {
  const [opened, { toggle }] = useDisclosure()

  return (
    <AppShell
      header={{ height: '4rem' }}
      navbar={{
        width: '14rem',
        breakpoint: 'sm',
        collapsed: { mobile: !opened },
      }}
      padding="sm"
    >
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Group>
            <ActionIcon
              onClick={toggle}
              hiddenFrom="sm"
              variant="transparent"
              color="dimmed"
              aria-expanded={opened}
            >
              {opened ? (
                <CloseCircle weight="LineDuotone" />
              ) : (
                <HamburgerMenu weight="LineDuotone" />
              )}
            </ActionIcon>
            <UnstyledButton component={Link} href="/">
              <Group gap="sm">
                <Image src="/logo.svg" alt="Logo" w={32} h={32} />
                <Title size="h4">Mantine</Title>
              </Group>
            </UnstyledButton>
          </Group>
          <Menu shadow="md" position="bottom-end">
            <Menu.Target>
              <Avatar src={user.avatar} radius="xl">
                {user.name.slice(0, 2).toUpperCase()}
              </Avatar>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Label>Account</Menu.Label>
              <Menu.Item leftSection={<User size={20} weight="BoldDuotone" />}>Profile</Menu.Item>
              <Menu.Item leftSection={<Settings size={20} weight="BoldDuotone" />}>
                Settings
              </Menu.Item>
              <Menu.Item leftSection={<Bell size={20} weight="BoldDuotone" />}>
                Notifications
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item leftSection={<Logout2 size={20} weight="BoldDuotone" />} color="red">
                Sign out
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar>
        <ScrollArea>
          {navigationData.map((item) => {
            const hasChildren = item.children && item.children.length > 0
            if (hasChildren) {
              return (
                <NavLink
                  key={item.label}
                  label={item.label}
                  leftSection={<item.icon size={20} weight="BoldDuotone" />}
                  rightSection={
                    <AltArrowRight size={18} weight="BoldDuotone" className="mantine-rotate-rtl" />
                  }
                  childrenOffset="xl"
                >
                  {item.children?.map((child) => (
                    <NavLink
                      key={child.label}
                      label={child.label}
                      href={child.href}
                      component={Link}
                    />
                  ))}
                </NavLink>
              )
            }

            if (!item.href) {
              return null
            }

            return (
              <NavLink
                key={item.label}
                label={item.label}
                href={item.href}
                component={Link}
                leftSection={<item.icon size={20} weight="BoldDuotone" />}
              />
            )
          })}
        </ScrollArea>
      </AppShell.Navbar>

      <AppShell.Main>
        <Box p="md">
          <Title order={1} mb="xl">
            Typography Showcase
          </Title>

          {/* Headings Section */}
          <Box mb="xl">
            <Title order={2} mb="md" c="blue.7">
              Headings
            </Title>
            <Title order={1} mb="xs">
              Heading 1 - Main Title
            </Title>
            <Title order={2} mb="xs">
              Heading 2 - Section Title
            </Title>
            <Title order={3} mb="xs">
              Heading 3 - Subsection Title
            </Title>
            <Title order={4} mb="xs">
              Heading 4 - Minor Heading
            </Title>
            <Title order={5} mb="xs">
              Heading 5 - Small Heading
            </Title>
            <Title order={6} mb="xs">
              Heading 6 - Smallest Heading
            </Title>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Heading Variants
            </Title>
            <Title order={2} mb="xs" fw={300}>
              Light Weight Heading
            </Title>
            <Title order={2} mb="xs" fw={700}>
              Bold Weight Heading
            </Title>
            <Title order={2} mb="xs" fs="italic">
              Italic Heading
            </Title>
            <Title order={2} mb="xs" td="underline">
              Underlined Heading
            </Title>
            <Title order={2} mb="xs" tt="uppercase">
              Uppercase Heading
            </Title>
            <Title order={2} mb="xs" lineClamp={1} maw={300}>
              Very Long Heading That Should Be Clamped to One Line Only
            </Title>
          </Box>

          {/* Body Text Section */}
          <Box mb="xl">
            <Title order={2} mb="md" c="blue.7">
              Body Text
            </Title>

            <Title order={3} mb="xs" c="dimmed">
              Text Sizes
            </Title>
            <Text size="xs" mb="xs">
              Extra Small Text (xs)
            </Text>
            <Text size="sm" mb="xs">
              Small Text (sm)
            </Text>
            <Text size="md" mb="xs">
              Medium Text (md) - Default
            </Text>
            <Text size="lg" mb="xs">
              Large Text (lg)
            </Text>
            <Text size="xl" mb="xs">
              Extra Large Text (xl)
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Text Weights
            </Title>
            <Text fw={100} mb="xs">
              Thin Weight (100)
            </Text>
            <Text fw={300} mb="xs">
              Light Weight (300)
            </Text>
            <Text fw={400} mb="xs">
              Normal Weight (400)
            </Text>
            <Text fw={500} mb="xs">
              Medium Weight (500)
            </Text>
            <Text fw={700} mb="xs">
              Bold Weight (700)
            </Text>
            <Text fw={900} mb="xs">
              Black Weight (900)
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Text Styles
            </Title>
            <Text mb="xs">Normal text</Text>
            <Text fs="italic" mb="xs">
              Italic text
            </Text>
            <Text td="underline" mb="xs">
              Underlined text
            </Text>
            <Text td="line-through" mb="xs">
              Strikethrough text
            </Text>
            <Text tt="uppercase" mb="xs">
              Uppercase text
            </Text>
            <Text tt="lowercase" mb="xs">
              LOWERCASE TEXT
            </Text>
            <Text tt="capitalize" mb="xs">
              capitalize text
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Text Alignment
            </Title>
            <Text ta="left" mb="xs">
              Left aligned text
            </Text>
            <Text ta="center" mb="xs">
              Center aligned text
            </Text>
            <Text ta="right" mb="xs">
              Right aligned text
            </Text>
            <Text ta="justify" mb="xs">
              Justified text that spans multiple lines to demonstrate how text justification works
              when there is enough content to wrap to multiple lines in the container.
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Line Height & Spacing
            </Title>
            <Text lh="xs" mb="xs">
              Text with extra small line height. This text has multiple lines to show how the line
              height affects the spacing between lines of text.
            </Text>
            <Text lh="xl" mb="xs">
              Text with extra large line height. This text has multiple lines to show how the line
              height affects the spacing between lines of text.
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Text Truncation
            </Title>
            <Text lineClamp={2} maw={400} mb="xs">
              This is a long text that will be truncated after two lines. Lorem ipsum dolor sit
              amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et
              dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation.
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Inline Elements
            </Title>
            <Text mb="xs">
              This text contains{' '}
              <Text span fw={700}>
                bold inline text
              </Text>{' '}
              and{' '}
              <Text span fs="italic" c="blue">
                colored italic text
              </Text>{' '}
              within the same paragraph.
            </Text>
          </Box>

          {/* Interactive Text Section */}
          <Box mb="xl">
            <Title order={2} mb="md" c="blue.7">
              Interactive Text
            </Title>

            <Title order={3} mb="xs" c="dimmed">
              Links & Anchors
            </Title>
            <Text mb="xs">
              This paragraph contains a{' '}
              <Text component="a" href="#" c="blue" td="underline">
                regular link
              </Text>{' '}
              and an{' '}
              <Text component="a" href="#" c="red" fw={500}>
                emphasized link
              </Text>{' '}
              within the text.
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Highlighted Text
            </Title>
            <Text mb="xs">
              This text has{' '}
              <Text span bg="yellow.2" px={4}>
                highlighted background
              </Text>{' '}
              and{' '}
              <Text span bg="red.1" c="red.8" px={4}>
                colored highlight
              </Text>{' '}
              sections.
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Interactive States
            </Title>
            <Text mb="xs" style={{ cursor: 'pointer' }} c="blue.6">
              Clickable text that changes color on hover
            </Text>
            <Text mb="xs" c="dimmed">
              Dimmed text for secondary information
            </Text>
            <Text mb="xs" c="bright">
              Bright text that adapts to color scheme
            </Text>
          </Box>

          {/* Specialized Text Section */}
          <Box mb="xl">
            <Title order={2} mb="md" c="blue.7">
              Specialized Text
            </Title>

            <Title order={3} mb="xs" c="dimmed">
              Code & Monospace
            </Title>
            <Text mb="xs">
              Inline code:{' '}
              <Text span ff="monospace" bg="gray.1" px={4} py={2} style={{ borderRadius: 4 }}>
                const variable = 'value'
              </Text>
            </Text>
            <Text
              ff="monospace"
              bg="gray.0"
              p="md"
              style={{ borderRadius: 8, border: '1px solid var(--mantine-color-gray-3)' }}
              mb="md"
            >
              {`function example() {
  console.log('This is a code block');
  return true;
}`}
            </Text>

            <Title order={3} mb="xs" c="dimmed">
              Blockquotes
            </Title>
            <Box
              bg="gray.0"
              p="md"
              style={{
                borderLeft: '4px solid var(--mantine-color-blue-5)',
                borderRadius: 4,
              }}
              mb="md"
            >
              <Text fs="italic" c="gray.7" mb="xs">
                "The best way to predict the future is to create it."
              </Text>
              <Text size="sm" c="dimmed">
                — Peter Drucker
              </Text>
            </Box>

            <Title order={3} mb="xs" c="dimmed">
              Lists
            </Title>
            <Box mb="md">
              <Text fw={500} mb="xs">
                Unordered List:
              </Text>
              <Box component="ul" pl="md">
                <Text component="li" mb="xs">
                  First item in the list
                </Text>
                <Text component="li" mb="xs">
                  Second item with more content
                </Text>
                <Text component="li" mb="xs">
                  Third item
                </Text>
                <Box component="ul" pl="md">
                  <Text component="li" mb="xs">
                    Nested item one
                  </Text>
                  <Text component="li" mb="xs">
                    Nested item two
                  </Text>
                </Box>
              </Box>
            </Box>

            <Box mb="md">
              <Text fw={500} mb="xs">
                Ordered List:
              </Text>
              <Box component="ol" pl="md">
                <Text component="li" mb="xs">
                  First numbered item
                </Text>
                <Text component="li" mb="xs">
                  Second numbered item
                </Text>
                <Text component="li" mb="xs">
                  Third numbered item
                </Text>
              </Box>
            </Box>
          </Box>

          {/* Color Variants Section */}
          <Box mb="xl">
            <Title order={2} mb="md" c="blue.7">
              Color Variants
            </Title>

            <Title order={3} mb="xs" c="dimmed">
              Semantic Colors
            </Title>
            <Text mb="xs">
              Primary blue text
            </Text>
            <Text c="green" mb="xs">
              Success green text
            </Text>
            <Text c="red" mb="xs">
              Error red text
            </Text>
            <Text c="orange" mb="xs">
              Warning orange text
            </Text>
            <Text c="yellow" mb="xs">
              Caution yellow text
            </Text>
            <Text c="purple" mb="xs">
              Info purple text
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Color Shades
            </Title>
            <Text c="blue.1" mb="xs">
              Blue shade 1 (lightest)
            </Text>
            <Text c="blue.3" mb="xs">
              Blue shade 3
            </Text>
            <Text c="blue.5" mb="xs">
              Blue shade 5 (medium)
            </Text>
            <Text c="blue.7" mb="xs">
              Blue shade 7
            </Text>
            <Text c="blue.9" mb="xs">
              Blue shade 9 (darkest)
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Special Colors
            </Title>
            <Text c="dimmed" mb="xs">
              Dimmed text (theme-aware)
            </Text>
            <Text c="bright" mb="xs">
              Bright text (theme-aware)
            </Text>
            <Text c="dark" mb="xs">
              Dark text
            </Text>
            <Text c="gray" mb="xs">
              Gray text
            </Text>
          </Box>

          {/* Typography Utilities Section */}
          <Box mb="xl">
            <Title order={2} mb="md" c="blue.7">
              Typography Utilities
            </Title>

            <Title order={3} mb="xs" c="dimmed">
              Font Families
            </Title>
            <Text mb="xs">Default font family text</Text>
            <Text ff="monospace" mb="xs">
              Monospace font family text
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Text Wrapping
            </Title>
            <Box maw={300} mb="md">
              <Text mb="xs">
                This text will wrap normally when it reaches the container boundary and continues on
                the next line.
              </Text>
              <Text style={{ wordBreak: 'break-all' }} mb="xs">
                ThisTextWillBreakAtAnyCharacterWhenItReachesTheContainerBoundary
              </Text>
            </Box>

            <Title order={3} mb="xs" c="dimmed">
              Responsive Text
            </Title>
            <Text size="sm" mb="xs" style={{ fontSize: 'clamp(0.875rem, 2vw, 1.125rem)' }}>
              This text changes size based on screen size
            </Text>

            <Title order={3} mt="md" mb="xs" c="dimmed">
              Text with Custom Styling
            </Title>
            <Text
              variant="gradient"
              fw={700}
              size="xl"
              mb="xs"
            >
              Gradient Text Effect
            </Text>

            <Text
              style={{
                textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
                fontSize: '1.2rem',
              }}
              mb="xs"
            >
              Text with shadow effect
            </Text>
          </Box>
        </Box>
      </AppShell.Main>
    </AppShell>
  )
}
